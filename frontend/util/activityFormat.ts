import { Activity, Activity2 } from '@quarterback/types';

export default function activityFormat(activity: Activity | Activity2): string {
    if ('type' in activity) {
        switch (activity.type) {
            case 'news':
                return 'Media';
            case 'asx-announcement':
                return 'Announcement';
        }
    }

    if ('isBroadcast' in activity) {
        return activity.isBroadcast ? 'Broadcast' : 'Chatter';
    }

    return 'Chatter';
}
