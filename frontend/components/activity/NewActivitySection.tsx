import { useActivityForm } from '@/contexts/ActivityFormProvider';
import NewActivityButton from '@/components/ui/NewActivityButton';
import ManualActivityForm from '@/components/forms/ManualActivityForm';

interface NewActivitySectionProps {
    buttonSize?: 'sm' | 'md' | 'lg';
    buttonClassName?: string;
    disabled?: boolean;
}

export default function NewActivitySection({
    buttonSize = 'sm',
    buttonClassName,
    disabled = false
}: NewActivitySectionProps) {
    const { openForm } = useActivityForm();

    return (
        <>
            <NewActivityButton
                onClick={openForm}
                size={buttonSize}
                className={buttonClassName}
                disabled={disabled}
            />
            <ManualActivityForm />
        </>
    );
}
