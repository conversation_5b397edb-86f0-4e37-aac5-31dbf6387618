import { useActivityForm } from '@/contexts/ActivityFormProvider';
import ManualActivityFormFactory from '@/app/(authenticated)/investors/activities/ManualActivityFormFactory';

export default function ManualActivityForm() {
    const { isFormOpen, closeForm, onSubmit } = useActivityForm();

    const handleSubmit = async (data: any) => {
        if (onSubmit) {
            return await onSubmit(data);
        }
        return Promise.resolve();
    };

    return (
        <ManualActivityFormFactory
            addingActivity={isFormOpen}
            setAddingActivity={(open: boolean) => {
                if (!open) {
                    closeForm();
                }
            }}
            onSubmit={handleSubmit}
        />
    );
}
