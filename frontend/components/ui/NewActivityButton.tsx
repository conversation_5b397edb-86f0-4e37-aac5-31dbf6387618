import { PlusIcon } from '@heroicons/react/24/outline';
import Button from './Button';

interface NewActivityButtonProps {
    onClick: () => void;
    disabled?: boolean;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

export default function NewActivityButton({
    onClick,
    disabled = false,
    size = 'sm',
    className
}: NewActivityButtonProps) {
    return (
        <Button
            variant="primary"
            onClick={onClick}
            size={size}
            disabled={disabled}
            icon={<PlusIcon className="size-4" />}
            className={className}>
            New Activity
        </Button>
    );
}
